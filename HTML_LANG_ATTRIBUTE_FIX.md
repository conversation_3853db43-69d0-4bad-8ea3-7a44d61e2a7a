# HTML Lang Attribute Fix for Internationalization

This document outlines the changes made to fix the missing `lang` attribute on the HTML element while properly handling internationalization (i18n).

## Issue Identified

The HTML element was missing the `lang` attribute, which is crucial for:
- **Accessibility**: Screen readers need to know the page language
- **SEO**: Search engines use this to understand content language
- **Browser behavior**: Language-specific features like spell checking
- **Compliance**: WCAG guidelines require proper language declaration

## Challenge with i18n

The main challenge was that the root layout (`src/app/layout.tsx`) doesn't have access to the current locale, which is only available in the locale-specific layout (`src/app/[locale]/layout.tsx`).

## Solution Implemented

### 1. Restructured Layout Architecture

**Before:**
```
src/app/layout.tsx (contains <html> element without lang)
└── src/app/[locale]/layout.tsx (contains NextIntlClientProvider)
```

**After:**
```
src/app/layout.tsx (contains <html> with default lang="bs")
└── src/app/[locale]/layout.tsx (contains NextIntlClientProvider + HtmlLangSetter)
    └── HtmlLangSetter (client component that dynamically updates lang attribute)
```

### 2. Created Locale Utilities (`src/lib/locale.ts`)

Added comprehensive locale management functions:
- `getHtmlLang()` - Maps locale codes to HTML lang values
- `getLocaleFromPathname()` - Extracts locale from URL
- `isValidLocale()` - Validates locale codes
- `getLocaleDisplayName()` - Gets human-readable names
- `getLocaleFlagCode()` - Gets flag codes for UI

### 3. Created HtmlLangSetter Component (`src/components/HtmlLangSetter.tsx`)

**Key Features:**
- Client component that uses `useLocale()` from next-intl
- Dynamically updates HTML `lang` attribute via `useEffect`
- Runs on every locale change
- No visual rendering (returns null)

### 4. Updated Locale Layout (`src/app/[locale]/layout.tsx`)

**Key Changes:**
- Added `<HtmlLangSetter />` component to handle dynamic lang attribute
- Maintained locale-specific metadata generation
- Clean separation of server and client concerns

### 4. Enhanced Metadata Generation

Added `generateMetadata()` function that provides:
- Locale-specific titles and descriptions
- OpenGraph metadata with proper locale
- Twitter Card metadata
- Language alternates for SEO

### 5. Updated Navigation Component

Refactored to use the new locale utilities for consistency:
- Uses `getLocaleDisplayName()` for language names
- Uses `getLocaleFlagCode()` for flag icons
- Cleaner, more maintainable code

## Language Mapping

The system now properly maps locale codes to HTML lang attributes:

| Locale Code | HTML Lang | Language | Flag |
|-------------|-----------|----------|------|
| `bs` | `bs` | Bosanski | 🇧🇦 BA |
| `en` | `en` | English | 🇺🇸 US |
| `de` | `de` | Deutsch | 🇩🇪 DE |

## Metadata by Locale

Each locale now has proper metadata:

### Bosnian (bs)
- **Title**: "AlbatrosDoc - Pouzdana rješenja za vašu dokumentaciju"
- **Description**: "Pouzdana rješenja za vašu dokumentaciju i administrativne potrebe..."

### English (en)
- **Title**: "AlbatrosDoc - Reliable Solutions for Your Documentation"
- **Description**: "Reliable solutions for your documentation and administrative needs..."

### German (de)
- **Title**: "AlbatrosDoc - Zuverlässige Lösungen für Ihre Dokumentation"
- **Description**: "Zuverlässige Lösungen für Ihre Dokumentations- und Verwaltungsbedürfnisse..."

## SEO Benefits

1. **Language Alternates**: Proper `hreflang` attributes for search engines
2. **Locale-specific OpenGraph**: Social media sharing with correct language
3. **Proper HTML lang**: Search engines understand content language
4. **Twitter Cards**: Enhanced social media presence

## Accessibility Improvements

1. **Screen Reader Support**: Proper language announcement
2. **Browser Features**: Correct spell checking and text-to-speech
3. **WCAG Compliance**: Meets accessibility guidelines
4. **User Experience**: Better language-specific browser behavior

## Technical Implementation

### HTML Structure
```html
<html lang="bs"> <!-- Default, then dynamically updated -->
  <head>
    <!-- Service worker and optimization scripts -->
  </head>
  <body>
    <NextIntlClientProvider>
      <HtmlLangSetter /> <!-- Updates lang attribute -->
      <!-- App content -->
    </NextIntlClientProvider>
  </body>
</html>
```

### Locale Detection Flow
1. URL path analyzed by middleware
2. Locale extracted from `[locale]` segment
3. Validated against supported locales
4. `useLocale()` hook provides locale to client components
5. `HtmlLangSetter` updates HTML lang attribute dynamically
6. Proper lang attribute applied on client-side hydration

## Testing

To verify the fix:

1. **View Source**: Check `<html lang="xx">` attribute
2. **DevTools**: Inspect HTML element
3. **Lighthouse**: Accessibility audit should pass
4. **Screen Readers**: Test language announcement
5. **SEO Tools**: Verify language detection

## Browser Support

- ✅ All modern browsers support HTML lang attribute
- ✅ Screen readers recognize language changes
- ✅ Search engines index with proper language
- ✅ Social media platforms use correct metadata

## Maintenance

The locale utilities are centralized in `src/lib/locale.ts`, making it easy to:
- Add new languages
- Update language mappings
- Maintain consistency across components
- Extend functionality

This implementation ensures full compliance with web standards while maintaining the flexibility of the internationalization system.
