import createMiddleware from 'next-intl/middleware';
import { NextRequest } from 'next/server';

const intlMiddleware = createMiddleware({
  // A list of all locales that are supported
  locales: ['bs', 'en', 'de'],

  // Used when no locale matches
  defaultLocale: 'bs',

  // Don't use a prefix for the default locale
  localePrefix: {
    mode: 'as-needed'
  },

  // Disable locale cookie to prevent it from overriding the default
  localeCookie: false,

  // Disable automatic locale detection from browser headers
  localeDetection: false
});

export default function middleware(request: NextRequest) {
  // Skip middleware for localized routes that should be handled by rewrites
  const pathname = request.nextUrl.pathname;
  if (pathname === '/o-nama' || pathname === '/uber-uns' || pathname === '/usluge' || pathname === '/dienstleistungen' || pathname === '/kontakt') {
    return;
  }

  const response = intlMiddleware(request);

  // Add bfcache-friendly headers
  if (response) {
    response.headers.set('Cache-Control', 'public, max-age=0, must-revalidate');
    // Ensure no unload handlers that could prevent bfcache
    response.headers.set('X-Content-Type-Options', 'nosniff');
  }

  return response;
}

export const config = {
  // Match only internationalized pathnames
  matcher: ['/', '/(de|en)/:path*']
};
