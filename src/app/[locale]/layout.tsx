import { NextIntlClientProvider } from 'next-intl';
import { getMessages } from 'next-intl/server';
import { getHtmlLang } from '@/lib/locale';
import type { Metadata } from 'next';
import HtmlLangSetter from '@/components/HtmlLangSetter';

// Generate locale-specific metadata
export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: string }>;
}): Promise<Metadata> {
  const { locale } = await params;

  const metadataByLocale: Record<string, { title: string; description: string }> = {
    'bs': {
      title: 'AlbatrosDoc - Pouzdana rješenja za vašu dokumentaciju',
      description: 'Pouzdana rješenja za vašu dokumentaciju i administrativne potrebe. Brza, sigurna i profesionalna usluga nabavke i dostave dokumenata.'
    },
    'en': {
      title: 'AlbatrosDoc - Reliable Solutions for Your Documentation',
      description: 'Reliable solutions for your documentation and administrative needs. Fast, secure and professional document procurement and delivery services.'
    },
    'de': {
      title: 'AlbatrosDoc - Zuverlässige Lösungen für Ihre Dokumentation',
      description: 'Zuverlässige Lösungen für Ihre Dokumentations- und Verwaltungsbedürfnisse. Schnelle, sichere und professionelle Dokumentenbeschaffung und -zustellung.'
    }
  };

  const metadata = metadataByLocale[locale] || metadataByLocale['bs'];

  return {
    title: metadata.title,
    description: metadata.description,
    openGraph: {
      title: metadata.title,
      description: metadata.description,
      locale: getHtmlLang(locale),
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title: metadata.title,
      description: metadata.description,
    },
    alternates: {
      languages: {
        'bs': '/',
        'en': '/en',
        'de': '/de',
      },
    },
  };
}

export default async function LocaleLayout({
  children,
  params
}: {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}) {
  await params; // Ensure params are awaited for Next.js
  const messages = await getMessages();

  return (
    <NextIntlClientProvider messages={messages}>
      <HtmlLangSetter />
      {children}
    </NextIntlClientProvider>
  );
}
