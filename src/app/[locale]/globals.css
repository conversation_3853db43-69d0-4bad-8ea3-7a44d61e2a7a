@import "tailwindcss";
@import "../../styles/features-gradient.css";
@import "../../styles/cta-gradient.css";
@import "../../styles/contact-hero-gradient.css";
@import "../../styles/contact-info-gradient.css";

:root {
  /* AlbatrosDoc Color Palette */
  --color-black: #000000;
  --color-indigo-dye: #1b3f5f;
  --color-moonstone: #5c9ead;
  --color-ivory: #f5fbef;
  --color-carmine: #931621;

  /* Base colors */
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-metro-sans);

  /* AlbatrosDoc Custom Colors */
  --color-albatros-black: var(--color-black);
  --color-albatros-indigo-dye: var(--color-indigo-dye);
  --color-albatros-moonstone: var(--color-moonstone);
  --color-albatros-ivory: var(--color-ivory);
  --color-albatros-carmine: var(--color-carmine);

  /* Brand Color Variants for Tailwind */
  --color-brand-primary: var(--color-indigo-dye);
  --color-brand-primary-light: var(--color-moonstone);
  --color-brand-secondary: var(--color-carmine);
  --color-brand-accent: var(--color-ivory);
}

* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  background: #000000;
  color: var(--foreground);
  font-family: var(--font-metro-sans), Arial, Helvetica, sans-serif;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
}

/* Modern selection styling */
::selection {
  background: rgba(27, 63, 95, 0.2);
  color: var(--color-ivory);
}

/* Smooth focus transitions */
*:focus {
  outline: none;
}

*:focus-visible {
  outline: 2px solid var(--color-moonstone);
  outline-offset: 2px;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, var(--color-black), var(--color-indigo-dye));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, var(--color-indigo-dye), var(--color-moonstone));
}

/* Enhanced animations */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes glow {
  0%, 100% { box-shadow: 0 0 20px rgba(92, 158, 173, 0.3); }
  50% { box-shadow: 0 0 30px rgba(92, 158, 173, 0.6); }
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

@keyframes fade-in {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

@keyframes slide-in-up {
  0% { transform: translateY(20px); }
  100% { transform: translateY(0); }
}

@keyframes slide-in-down {
  0% { transform: translateY(-20px); }
  100% { transform: translateY(0); }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

.animate-shimmer {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

/* Critical above-the-fold animations - no opacity changes */
.animate-slide-in-nav {
  animation: slide-in-down 0.8s ease-out 0s both;
}

.animate-slide-in-hero {
  animation: slide-in-up 1s ease-out 0.2s both;
}

/* Ensure critical content is always visible for Lighthouse */
.critical-content {
  opacity: 1 !important;
  visibility: visible !important;
}

/* Progressive enhancement for non-critical animations */
@media (prefers-reduced-motion: no-preference) {
  .enhanced-animation {
    transition: all 0.6s ease-out;
  }
}

/* Non-critical animations can still use opacity */
.animate-fade-in {
  animation: fade-in 1.2s ease-out 0.4s both;
}

.animate-fade-in-hero-bg {
  animation: fade-in 1.5s ease-out 0s both;
}

.animate-fade-in-short {
  animation: fade-in 0.2s ease-out 0s both;
}

/* Glass morphism effect */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Gradient text */
.gradient-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Modern button styles */
.btn-modern {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.btn-modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-modern:hover::before {
  left: 100%;
}



/* Flag icon optimizations for LCP */
.flag-icon {
  display: inline-block;
  width: 20px;
  height: 16px;
  flex-shrink: 0;
  contain: layout style paint;
  will-change: auto;
}

.flag-icon svg {
  width: 100%;
  height: 100%;
  display: block;
}

.grain {
  background-color: transparent;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 600 600'%3E%3Cfilter id='a'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='.65' numOctaves='3' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23a)'/%3E%3C/svg%3E");
  background-repeat: repeat;
  background-size: 182px;
  opacity: 0.45;
  top: 0;
  left: 0;
  position: absolute;
  width: 100%;
  height: 100%;
}