import type { <PERSON>ada<PERSON> } from "next";
import localFont from "next/font/local";
import "./[locale]/globals.css";

const metroSans = localFont({
  src: [
    // Light weights
    {
      path: '../../public/fonts/metro-sans/MetroSans-Light.ttf',
      weight: '300',
      style: 'normal',
    },
    {
      path: '../../public/fonts/metro-sans/MetroSans-LightItalic.ttf',
      weight: '300',
      style: 'italic',
    },
    // Book weights (normal)
    {
      path: '../../public/fonts/metro-sans/MetroSans-Book.ttf',
      weight: '400',
      style: 'normal',
    },
    {
      path: '../../public/fonts/metro-sans/MetroSans-BookItalic.ttf',
      weight: '400',
      style: 'italic',
    },
    // Regular weights (also normal, but different variant)
    {
      path: '../../public/fonts/metro-sans/MetroSans-Regular.ttf',
      weight: '400',
      style: 'normal',
    },
    {
      path: '../../public/fonts/metro-sans/MetroSans-RegularItalic.ttf',
      weight: '400',
      style: 'italic',
    },
    // Medium weights
    {
      path: '../../public/fonts/metro-sans/MetroSans-Medium.ttf',
      weight: '500',
      style: 'normal',
    },
    {
      path: '../../public/fonts/metro-sans/MetroSans-MediumItalic.ttf',
      weight: '500',
      style: 'italic',
    },
    // Semi-bold weights
    {
      path: '../../public/fonts/metro-sans/MetroSans-SemiBold.ttf',
      weight: '600',
      style: 'normal',
    },
    {
      path: '../../public/fonts/metro-sans/MetroSans-SemiBoldItalic.ttf',
      weight: '600',
      style: 'italic',
    },
    // Bold weights
    {
      path: '../../public/fonts/metro-sans/MetroSans-Bold.ttf',
      weight: '700',
      style: 'normal',
    },
    {
      path: '../../public/fonts/metro-sans/MetroSans-BoldItalic.ttf',
      weight: '700',
      style: 'italic',
    },
  ],
  variable: '--font-metro-sans',
  display: 'swap',
});

export const metadata: Metadata = {
  title: "AlbatrosDoc",
  description: "Pouzdana rješenja za vašu dokumentaciju i administrativne potrebe",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="bs" suppressHydrationWarning>
      <head>
        <script
          dangerouslySetInnerHTML={{
            __html: `
              // Service Worker registration
              if ('serviceWorker' in navigator) {
                window.addEventListener('load', function() {
                  navigator.serviceWorker.register('/sw.js')
                    .then(function(registration) {
                      console.log('SW registered: ', registration);
                    })
                    .catch(function(registrationError) {
                      console.log('SW registration failed: ', registrationError);
                    });
                });
              }

              // BFCache optimization
              window.addEventListener('DOMContentLoaded', function() {
                // Remove any beforeunload/unload listeners that might prevent bfcache
                window.removeEventListener('beforeunload', function() {});
                window.removeEventListener('unload', function() {});

                // Log bfcache events
                window.addEventListener('pageshow', function(event) {
                  if (event.persisted) {
                    console.log('Page restored from bfcache');
                  }
                });

                window.addEventListener('pagehide', function(event) {
                  if (event.persisted) {
                    console.log('Page stored in bfcache');
                  }
                });
              });
            `,
          }}
        />
      </head>
      <body
        className={`${metroSans.variable} antialiased font-metro-sans`}
        suppressHydrationWarning
      >
        {children}
      </body>
    </html>
  );
}
