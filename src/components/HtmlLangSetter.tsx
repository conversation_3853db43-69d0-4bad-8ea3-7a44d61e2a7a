'use client';

import { useEffect } from 'react';
import { useLocale } from 'next-intl';
import { getHtmlLang } from '@/lib/locale';

/**
 * Client component that sets the HTML lang attribute dynamically
 * based on the current locale from next-intl
 */
export default function HtmlLangSetter() {
  const locale = useLocale();

  useEffect(() => {
    // Set the lang attribute on the HTML element
    const htmlElement = document.documentElement;
    const langValue = getHtmlLang(locale);
    
    if (htmlElement) {
      htmlElement.setAttribute('lang', langValue);
    }
  }, [locale]);

  // This component doesn't render anything
  return null;
}
