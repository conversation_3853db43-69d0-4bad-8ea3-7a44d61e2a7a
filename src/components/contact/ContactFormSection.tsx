'use client';

import { useTranslations } from 'next-intl';
import { useInView } from 'react-intersection-observer';
import { useState } from 'react';

const ContactFormSection = () => {
  const t = useTranslations('contactPage.form');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');

  // Intersection observers for staggered animations
  const { ref: titleRef, inView: titleInView } = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });

  const { ref: formRef, inView: formInView } = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitStatus('idle');

    const form = e.currentTarget;
    const formData = new FormData(form);

    try {
      const response = await fetch('https://formspree.io/f/xkgzakqq', {
        method: 'POST',
        body: formData,
        headers: {
          'Accept': 'application/json',
          'Cache-Control': 'no-cache'
        },
        // Ensure this request doesn't interfere with bfcache
        cache: 'no-store'
      });

      if (response.ok) {
        setSubmitStatus('success');
        form.reset();
      } else {
        setSubmitStatus('error');
      }
    } catch {
      setSubmitStatus('error');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <section className="py-24 relative bg-albatros-ivory">
      <div className="grain"></div>
      <div className="max-w-7xl relative mx-auto px-6 lg:px-8">
        <div className="flex flex-col md:flex-row">
          {/* Left 1/3 - Small corner text */}
          <div className="mb-8 md:w-1/3 md:mb-0 pr-12">
            <div className="pt-8">
              <p className="text-sm text-black/60 font-light">
                <span className="font-bold">Albatros</span><br />
                {t('subtitle')}
              </p>
            </div>
          </div>

          {/* Right 2/3 - Form */}
          <div className="md:w-2/3">
            {/* Title */}
            <div
              ref={titleRef}
              className={`mb-12 transition-all duration-1000 ease-out delay-300 ${
                titleInView
                  ? 'opacity-100 translate-y-0'
                  : 'opacity-0 translate-y-8'
              }`}
            >
              <h2 className="text-4xl lg:text-6xl font-normal text-black leading-tight">
                {t('title')}
              </h2>
            </div>

            {/* Form */}
            <div
              ref={formRef}
              className={`transition-all duration-1000 ease-out delay-500 ${
                formInView
                  ? 'opacity-100 translate-y-0'
                  : 'opacity-0 translate-y-8'
              }`}
            >
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Name and Email Row */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="name" className="block text-sm font-medium text-black mb-2">
                      {t('name')}
                    </label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      required
                      placeholder={t('namePlaceholder')}
                      className="w-full px-4 py-3 border border-black/20 rounded-lg focus:ring-2 focus:ring-black/20 focus:border-black/40 transition-colors bg-white/70 backdrop-blur-sm"
                    />
                  </div>
                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-black mb-2">
                      {t('email')}
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      required
                      placeholder={t('emailPlaceholder')}
                      className="w-full px-4 py-3 border border-black/20 rounded-lg focus:ring-2 focus:ring-black/20 focus:border-black/40 transition-colors bg-white/70 backdrop-blur-sm"
                    />
                  </div>
                </div>

                {/* Phone and Subject Row */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="phone" className="block text-sm font-medium text-black mb-2">
                      {t('phone')}
                    </label>
                    <input
                      type="tel"
                      id="phone"
                      name="phone"
                      placeholder={t('phonePlaceholder')}
                      className="w-full px-4 py-3 border border-black/20 rounded-lg focus:ring-2 focus:ring-black/20 focus:border-black/40 transition-colors bg-white/70 backdrop-blur-sm"
                    />
                  </div>
                  <div>
                    <label htmlFor="subject" className="block text-sm font-medium text-black mb-2">
                      {t('subject')}
                    </label>
                    <input
                      type="text"
                      id="subject"
                      name="subject"
                      required
                      placeholder={t('subjectPlaceholder')}
                      className="w-full px-4 py-3 border border-black/20 rounded-lg focus:ring-2 focus:ring-black/20 focus:border-black/40 transition-colors bg-white/70 backdrop-blur-sm"
                    />
                  </div>
                </div>

                {/* Message */}
                <div>
                  <label htmlFor="message" className="block text-sm font-medium text-black mb-2">
                    {t('message')}
                  </label>
                  <textarea
                    id="message"
                    name="message"
                    required
                    rows={6}
                    placeholder={t('messagePlaceholder')}
                    className="w-full px-4 py-3 border border-black/20 rounded-lg focus:ring-2 focus:ring-black/20 focus:border-black/40 transition-colors bg-white/70 backdrop-blur-sm resize-none"
                  />
                </div>

                {/* Submit Button */}
                <div className="pt-4">
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="bg-black text-white px-8 py-4 rounded-lg font-medium hover:bg-black/90 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isSubmitting ? t('sending') : t('submit')}
                  </button>
                </div>

                {/* Status Messages */}
                {submitStatus === 'success' && (
                  <div className="mt-4 p-4 bg-green-100 border border-green-300 rounded-lg">
                    <p className="text-green-800">{t('success')}</p>
                  </div>
                )}
                {submitStatus === 'error' && (
                  <div className="mt-4 p-4 bg-red-100 border border-red-300 rounded-lg">
                    <p className="text-red-800">{t('error')}</p>
                  </div>
                )}
              </form>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ContactFormSection;
