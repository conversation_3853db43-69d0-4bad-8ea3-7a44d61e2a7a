// Locale utilities for internationalization

export type SupportedLocale = 'bs' | 'en' | 'de';

// Map locale codes to proper HTML lang attribute values
export function getHtmlLang(locale: string): string {
  const langMap: Record<string, string> = {
    'bs': 'bs', // Bosnian
    'en': 'en', // English
    'de': 'de', // German (Deutsch)
  };

  return langMap[locale] || 'bs'; // Default to Bosnian
}

// Get locale from pathname (for client-side usage)
export function getLocaleFromPathname(pathname: string): SupportedLocale {
  if (pathname.startsWith('/en')) return 'en';
  if (pathname.startsWith('/de')) return 'de';
  return 'bs'; // Default to Bosnian
}

// Validate if a locale is supported
export function isValidLocale(locale: string): locale is SupportedLocale {
  return ['bs', 'en', 'de'].includes(locale);
}

// Get the default locale
export function getDefaultLocale(): SupportedLocale {
  return 'bs';
}

// Get all supported locales
export function getSupportedLocales(): SupportedLocale[] {
  return ['bs', 'en', 'de'];
}

// Get locale display name
export function getLocaleDisplayName(locale: SupportedLocale): string {
  const displayNames: Record<SupportedLocale, string> = {
    'bs': 'Bosanski',
    'en': 'English',
    'de': 'Deutsch',
  };

  return displayNames[locale];
}

// Get locale flag code for flag icons
export function getLocaleFlagCode(locale: SupportedLocale): string {
  const flagCodes: Record<SupportedLocale, string> = {
    'bs': 'BA', // Bosnia and Herzegovina
    'en': 'US', // United States (for English)
    'de': 'DE', // Germany
  };

  return flagCodes[locale];
}
