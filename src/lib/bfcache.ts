// BFCache optimization utilities
// Helps ensure the page is eligible for back/forward cache

export function optimizeForBFCache() {
  if (typeof window === 'undefined') return;

  // Remove any existing beforeunload listeners that might prevent bfcache
  window.removeEventListener('beforeunload', () => {});
  
  // Remove any existing unload listeners that might prevent bfcache
  window.removeEventListener('unload', () => {});

  // Ensure we don't have any ongoing network requests when navigating away
  const originalFetch = window.fetch;
  const activeRequests = new Set<AbortController>();

  window.fetch = function(...args) {
    const controller = new AbortController();
    activeRequests.add(controller);

    const [input, init = {}] = args;
    const enhancedInit = {
      ...init,
      signal: controller.signal,
    };

    const promise = originalFetch(input, enhancedInit);
    
    promise.finally(() => {
      activeRequests.delete(controller);
    });

    return promise;
  };

  // Clean up active requests when page is about to be cached
  window.addEventListener('pagehide', (event) => {
    if (event.persisted) {
      // Page is being cached, abort any ongoing requests
      activeRequests.forEach(controller => {
        controller.abort();
      });
      activeRequests.clear();
    }
  });

  // Log bfcache events for debugging
  window.addEventListener('pageshow', (event) => {
    if (event.persisted) {
      console.log('Page restored from bfcache');
    }
  });

  window.addEventListener('pagehide', (event) => {
    if (event.persisted) {
      console.log('Page stored in bfcache');
    } else {
      console.log('Page not eligible for bfcache');
    }
  });
}

// Check if the page is eligible for bfcache
export function checkBFCacheEligibility() {
  if (typeof window === 'undefined') return;

  // Check for common bfcache blockers
  const blockers = [];

  // Check for beforeunload listeners
  if (window.onbeforeunload) {
    blockers.push('beforeunload listener detected');
  }

  // Check for unload listeners
  if (window.onunload) {
    blockers.push('unload listener detected');
  }

  // Check for open connections (this is a simplified check)
  if (navigator.serviceWorker && navigator.serviceWorker.controller) {
    // Service worker is active, which is good for caching
  }

  if (blockers.length > 0) {
    console.warn('BFCache blockers detected:', blockers);
  } else {
    console.log('Page appears to be bfcache eligible');
  }

  return blockers;
}
